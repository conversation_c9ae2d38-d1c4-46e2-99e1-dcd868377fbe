const InternshipApplicationSchema = new Schema({
  _id: { type: Schema.Types.ObjectId, auto: true },
  internship: {
    type: Schema.Types.ObjectId,
    ref: 'Internship',
    required: true
  },
  applicant: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  resume: {
    type: Schema.Types.ObjectId,
    ref: 'Assets',
    required: true
  },
  coverLetter: {
    type: String
  },
  answers: [{
    question: String,
    response: String
  }],
  experienceLevel: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced'],
    default: 'beginner'
  },
  status: {
    type: String,
    enum: [
      'applied',
      'under review',
      'shortlisted',
      'interview scheduled',
      'interviewed',
      'offered',
      'rejected',
      'withdrawn',
    ],
    default: 'applied'
  },
  appliedAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  withdrawnAt: {
    type: Date
  },
  feedback: {
    type: [String]
  },
  
  offerLetterLink: {
    type: String
  },
  
  rejectedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  
  isDeleted: {
    type: Boolean,
    default: false
  },
  deletedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  deletedAt: {
    type: Date
  }
});