const InternshipInterviewSchema = new Schema({
  _id: { type: Schema.Types.ObjectId, auto: true },
  internship: {
    type: Schema.Types.ObjectId,
    ref: 'Internship',
    required: true
  },
  application: {
    type: Schema.Types.ObjectId,
    ref: 'InternshipApplication',
    required: true
  },
  roundNumber: {
    type: Number,
    required: true
  },
  interviewer: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  interviewee: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  scheduledAt: {
    type: Date,
    required: true
  },
  rescheduledAt: {
    type: Date
  },
  rescheduledBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  requestedRescheduleReason: {
    type: String
  },
  timezone: {
    type: String,
    default: 'UTC'
  },
  mode: {
    type: String,
    enum: ['Online', 'Offline'],
    default: 'Online'
  },
  interviewMode: {
    type: String // e.g., Zoom/Meet link or physical address
  },
  platform: {
    type: String // e.g., <PERSON>m, Google Meet, Microsoft Teams
  },
  durationMinutes: {
    type: Number
  },
  intervieweeConfirmed: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['unscheduled', 'rescheduled', 'scheduled', 'cancelled'],
    default: 'scheduled'
  }, 
   feedbackSummary: {
    type: String
  }, 
  feedbackCategories: {
    communication: { type: Number, min: 1, max: 5 },
    problemSolving: { type: Number, min: 1, max: 5 },
    technicalKnowledge: { type: Number, min: 1, max: 5 },
    enthusiasm: { type: Number, min: 1, max: 5 },
    culturalFit: { type: Number, min: 1, max: 5 }
  },
  overallScore: {
    type: Number,
    min: 0,
    max: 100
  },
  decision: {
    type: String,
    enum: ['proceed', 'reject', 'on hold', 're-interview'],
    default: 'on hold'
  },
  decisionStatus:{
    type: String,
  },
  notes:{
    type: [String]
  },
  recordingLink: {
    type: String
  },
  attachments: {
    type: Schema.Types.ObjectId,
    ref: 'Assets',
    required: true
  },
  decisionNote: {
    type: String
  },
  isFlaggedForReview: {
    type: Boolean,
    default: false
  },
  createdBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  updatedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
  // Soft delete
  isDeleted: {
    type: Boolean,
    default: false
  },
  deletedBy: {
    type: Schema.Types.ObjectId,
    ref: 'User'
  },
  deletedAt: {
    type: Date
  }
});