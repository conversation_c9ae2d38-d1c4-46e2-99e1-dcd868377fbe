const InternshipSchema = new Schema({
    _id: { type: Schema.Types.ObjectId, auto: true },
    company: {
        type: Schema.Types.ObjectId,
        ref: 'Company',
        required: true
    },
    title: {
        type: String,
        required: true
    },
    slug: {
        type: String,
        unique: true
    },
    description: {
        type: String,
        required: true
    },
    responsibilities: {
        type: [String],
        required: true
    },
    location: {
        type: String,
        required: true
    },
    internshipType: {
        type: String,
        enum: ['In-office', 'Remote', 'Hybrid'],
        default: 'In-office'
    },
    duration: {
        type: String, // e.g., "3 months"
        required: true
    },
    startDate: {
        type: String, // e.g., "Immediately" or "From 1st June"
        required: true
    },
    endDate: {
        type: String // Optional: for exact end period
    },
    stipend: {
        type: String // e.g., "Negotiable" 
    },
    applicationDeadline: {
        type: Date,
        required: true
    },
    perks: [
        { type: String } // e.g., "Certificate", "Job offer", etc.
    ],

    workingHours: {
        type: String,
        default: 'Flexible'
    },
    skillsRequired: [
        { type: String }
    ],
    eligibilityCriteria: {
        type: String
    },
    qualification: {
        type: String, 
        default: 'Any'
    },
    disciplinesAllowed: [
        { type: String } // e.g., "Computer Science", "Design"
    ],
    selectionProcess: {
        type: [String]
    },
    numberOfOpenings: {
        type: Number,
        default: 1
    },
    applicationInstructions: {
        type: String // custom message or steps
    },
    additionalQuestions: [
        {
            question: String,
            isRequired: { type: Boolean, default: true }
        }
    ],
    preferredLanguages: [
        { type: String } // e.g., "English", "Hindi"
    ],
    remote: {
        type: Boolean,
        default: false
    },
    isActive: {
        type: Boolean,
        default: true
    },
    postedBy: {
        type: Schema.Types.ObjectId,
        ref: 'User' // HR or recruiter
    },
    postedAt: {
        type: Date,
        default: Date.now
    },
    expiresAt: {
        type: Date
    },
    views: {
        type: Number,
        default: 0
    },
    bookmarks: [{
        type: Schema.Types.ObjectId,
        ref: 'User'
    }],
    tags: [
        { type: String } // for filtering/search
    ],
    applicants: [{
        user: { type: Schema.Types.ObjectId, ref: 'User' },
        resume: String,
        coverLetter: String,
        portfolioLink: String,
        status: {
            type: String,
            enum: ['applied', 'reviewed', 'interview', 'rejected', 'hired'],
            default: 'applied'
        },
        appliedAt: { type: Date, default: Date.now }
    }],
    isDeleted: {
        type: Boolean,
        default: false
    },
    deletedBy: {
        type: Schema.Types.ObjectId,
        ref: 'User' // Or 'Admin' depending on your user roles
    },
    deletedAt: {
        type: Date
    },
    //......more fields
});